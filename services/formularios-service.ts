import { db, storage } from "@/lib/firebase";
import {
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  updateDoc,
  query,
  orderBy,
  serverTimestamp,
  where,
  onSnapshot,
  Unsubscribe,
  getDoc
} from "firebase/firestore";
import { ref, deleteObject } from "firebase/storage";
import { Formulario } from "@/components/formularios-table";
import { logDeletionActivity, logCreationActivity, logUpdateActivity, ActivityType } from "./activity-service";
import { getUser } from "./users-service";

const COLLECTION_NAME = "formularios";

// Adicionar um novo formulário
export async function addFormulario(formularioData: Omit<Formulario, "id">): Promise<string> {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...formularioData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de criação
    if (formularioData.uploadedBy) {
      const userInfo = await getUser(formularioData.uploadedBy);
      await logCreationActivity(
        ActivityType.FORMULARIO_UPLOADED,
        "Novo Formulário",
        `Carregou um formulário: ${formularioData.title}`,
        formularioData.uploadedBy,
        formularioData.teamId || userInfo?.teamId,
        {
          title: formularioData.title,
          fileType: formularioData.type,
          fileName: formularioData.name
        }
      );
    }

    return docRef.id;
  } catch (error) {
    console.error("Erro ao adicionar formulário:", error);
    throw error;
  }
}

// Adicionar múltiplos formulários
export async function addMultipleFormularios(formulariosData: Omit<Formulario, "id">[]): Promise<string[]> {
  try {
    const promises = formulariosData.map(async (formularioData) => {
      const docRef = await addDoc(collection(db, COLLECTION_NAME), {
        ...formularioData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      // Registar atividade de criação para cada formulário
      if (formularioData.uploadedBy) {
        const userInfo = await getUser(formularioData.uploadedBy);
        await logCreationActivity(
          ActivityType.FORMULARIO_UPLOADED,
          "Novo Formulário",
          `Carregou um formulário: ${formularioData.title}`,
          formularioData.uploadedBy,
          formularioData.teamId || userInfo?.teamId,
          {
            title: formularioData.title,
            fileType: formularioData.type,
            fileName: formularioData.name,
            groupId: formularioData.groupId,
            groupTitle: formularioData.groupTitle
          }
        );
      }

      return docRef.id;
    });

    const docIds = await Promise.all(promises);

    // Log a summary activity for the group upload
    if (formulariosData.length > 0 && formulariosData[0].uploadedBy) {
      const firstFormulario = formulariosData[0];
      const userInfo = await getUser(firstFormulario.uploadedBy);
      await logCreationActivity(
        ActivityType.FORMULARIO_UPLOADED,
        "Grupo de Formulários",
        `Carregou ${formulariosData.length} documentos: ${firstFormulario.groupTitle}`,
        firstFormulario.uploadedBy,
        firstFormulario.teamId || userInfo?.teamId,
        {
          groupTitle: firstFormulario.groupTitle,
          groupId: firstFormulario.groupId,
          documentCount: formulariosData.length,
          documents: formulariosData.map(f => ({ title: f.title, fileName: f.name }))
        }
      );
    }

    return docIds;
  } catch (error) {
    console.error("Erro ao adicionar múltiplos formulários:", error);
    throw error;
  }
}

// Obter todos os formulários
export async function getFormularios(userId?: string): Promise<Formulario[]> {
  try {
    let q;

    if (userId) {
      // Se um userId for fornecido, buscar apenas os formulários desse usuário
      q = query(
        collection(db, COLLECTION_NAME),
        where("uploadedBy", "==", userId),
        orderBy("uploadedAt", "desc")
      );
    } else {
      // Caso contrário, buscar todos os formulários
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy("uploadedAt", "desc")
      );
    }

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    } as Formulario));
  } catch (error) {
    console.error("Erro ao obter formulários:", error);
    throw error;
  }
}

// Atualizar um formulário existente
export async function updateFormulario(id: string, data: Partial<Formulario>, userId?: string) {
  try {
    const formularioRef = doc(db, COLLECTION_NAME, id);

    // Obter dados atuais do formulário para registar a atividade
    const formularioSnap = await getDoc(formularioRef);
    const formularioData = formularioSnap.exists() ? formularioSnap.data() : null;

    await updateDoc(formularioRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });

    // Registar atividade de atualização
    if (userId && formularioData) {
      const userInfo = await getUser(userId);
      await logUpdateActivity(
        ActivityType.FORMULARIO_UPDATED,
        "Formulário Atualizado",
        `Atualizou o formulário: ${formularioData.title || 'Sem título'}`,
        userId,
        formularioData.teamId || userInfo?.teamId,
        {
          title: formularioData.title,
          fileType: formularioData.type,
          fileName: formularioData.name
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao atualizar formulário:", error);
    throw error;
  }
}

// Excluir um formulário
export async function deleteFormulario(id: string, fileUrl: string, userId: string) {
  try {
    // Obter dados do formulário antes de excluir para registar a atividade
    const formularioRef = doc(db, COLLECTION_NAME, id);
    const formularioSnap = await getDoc(formularioRef);
    const formularioData = formularioSnap.exists() ? formularioSnap.data() : null;

    // Primeiro, excluir o documento do Firestore
    await deleteDoc(doc(db, COLLECTION_NAME, id));

    // Em seguida, tentar excluir o arquivo do Storage
    try {
      // Extrair o caminho do arquivo da URL
      // A URL completa do Firebase Storage contém um token de acesso e outros parâmetros
      const urlObj = new URL(fileUrl);
      const pathWithToken = urlObj.pathname;

      // O caminho está no formato /v0/b/BUCKET_NAME/o/ENCODED_PATH
      // Precisamos extrair apenas a parte ENCODED_PATH e decodificá-la
      const encodedPath = pathWithToken.split('/o/')[1];
      if (encodedPath) {
        const decodedPath = decodeURIComponent(encodedPath);
        console.log("Tentando excluir arquivo:", decodedPath);

        const fileRef = ref(storage, decodedPath);
        await deleteObject(fileRef);
      } else {
        console.error("Caminho do arquivo não encontrado na URL:", fileUrl);
      }
    } catch (storageError) {
      console.error("Erro ao excluir arquivo do Storage:", storageError);
      // Não lançar erro aqui, pois o documento já foi excluído
    }

    // Registar atividade de eliminação
    if (formularioData) {
      const userInfo = await getUser(userId);
      await logDeletionActivity(
        ActivityType.FORMULARIO_DELETED,
        "Formulário Eliminado",
        `Eliminou o formulário: ${formularioData.title || 'Sem título'}`,
        userId,
        userInfo?.teamId,
        {
          formularioTitle: formularioData.title,
          fileName: formularioData.name,
          fileType: formularioData.type
        }
      );
    }

    return true;
  } catch (error) {
    console.error("Erro ao excluir formulário:", error);
    throw error;
  }
}

// Escutar mudanças em formulários em tempo real
export function listenToFormularios(callback: (formularios: Formulario[]) => void, userId?: string): Unsubscribe {
  try {
    let q;

    if (userId) {
      // Se um userId for fornecido, escutar apenas os formulários desse usuário
      q = query(
        collection(db, COLLECTION_NAME),
        where("uploadedBy", "==", userId),
        orderBy("uploadedAt", "desc")
      );
    } else {
      // Caso contrário, escutar todos os formulários
      q = query(
        collection(db, COLLECTION_NAME),
        orderBy("uploadedAt", "desc")
      );
    }

    return onSnapshot(q, (querySnapshot) => {
      const formularios = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Formulario));

      callback(formularios);
    }, (error) => {
      console.error("Erro ao escutar formulários:", error);
    });
  } catch (error) {
    console.error("Erro ao configurar listener de formulários:", error);
    throw error;
  }
}
