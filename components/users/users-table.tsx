"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  IconDotsVertical,
  IconEdit,
  IconUserShield,
  IconUserEdit,
  IconTrash,
  IconMail,
  IconUsers,
  IconUserPlus,
  IconUserMinus,
  IconCheck,
  IconX,
  IconAlertTriangle,
  IconMoreHorizontal
} from "@tabler/icons-react";
import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/types/team";
import { useIsMobile } from "@/hooks/use-mobile";

interface User {
  id: string;
  name: string;
  email?: string;
  registrationNumber?: string;
  category?: string;
  role?: UserRole;
  teamId?: string;
  teamName?: string;
}

interface UsersTableProps {
  users: User[];
  onEditRole: (user: User) => void;
  onEditProfile: (user: User) => void;
  onDelete?: (user: User) => void;
  onSendPasswordReset?: (email: string) => void;
  teams?: { id: string; name: string }[];
  // Bulk actions
  onBulkDelete?: (userIds: string[]) => void;
  onBulkRoleChange?: (userIds: string[], role: UserRole, teamId?: string) => void;
  onBulkTeamAssign?: (userIds: string[], teamId: string) => void;
  onBulkPasswordReset?: (emails: string[]) => void;
}

export function UsersTable({
  users,
  onEditRole,
  onEditProfile,
  onDelete,
  onSendPasswordReset,
  teams = [],
  onBulkDelete,
  onBulkRoleChange,
  onBulkTeamAssign,
  onBulkPasswordReset
}: UsersTableProps) {
  // Estado para seleção múltipla
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);

  // Estado para confirmação de ações em bulk
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [isRoleChangeConfirmOpen, setIsRoleChangeConfirmOpen] = useState(false);
  const [pendingRoleChange, setPendingRoleChange] = useState<{ role: UserRole; teamId?: string } | null>(null);

  // Hook para detectar mobile
  const isMobile = useIsMobile();

  // Funções de manipulação de seleção
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
      setIsAllSelected(true);
    } else {
      setSelectedUsers([]);
      setIsAllSelected(false);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      const newSelected = [...selectedUsers, userId];
      setSelectedUsers(newSelected);
      setIsAllSelected(newSelected.length === users.length);
    } else {
      const newSelected = selectedUsers.filter(id => id !== userId);
      setSelectedUsers(newSelected);
      setIsAllSelected(false);
    }
  };

  const clearSelection = () => {
    setSelectedUsers([]);
    setIsAllSelected(false);
  };

  // Obter emails dos utilizadores selecionados
  const getSelectedEmails = () => {
    return users
      .filter(user => selectedUsers.includes(user.id) && user.email)
      .map(user => user.email!)
      .filter(Boolean);
  };

  // Handlers para confirmação de ações
  const handleBulkDeleteConfirm = () => {
    setIsDeleteConfirmOpen(true);
  };

  const handleBulkRoleChangeConfirm = (role: UserRole, teamId?: string) => {
    setPendingRoleChange({ role, teamId });
    setIsRoleChangeConfirmOpen(true);
  };

  const confirmBulkDelete = () => {
    onBulkDelete?.(selectedUsers);
    setIsDeleteConfirmOpen(false);
    clearSelection();
  };

  const confirmBulkRoleChange = () => {
    if (pendingRoleChange) {
      onBulkRoleChange?.(selectedUsers, pendingRoleChange.role, pendingRoleChange.teamId);
      setIsRoleChangeConfirmOpen(false);
      setPendingRoleChange(null);
      clearSelection();
    }
  };

  // Obter nomes dos utilizadores selecionados para exibição
  const getSelectedUserNames = () => {
    return users
      .filter(user => selectedUsers.includes(user.id))
      .map(user => user.name)
      .slice(0, 3); // Mostrar apenas os primeiros 3 nomes
  };

  // Obter texto do papel para exibição
  const getRoleDisplayText = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "Administrador";
      case UserRole.TEAM_LEADER:
        return "Líder de Equipa";
      case UserRole.TEAM_MEMBER:
        return "Membro de Equipa";
      default:
        return "Papel desconhecido";
    }
  };

  // Função para obter o nome da equipe pelo ID
  const getTeamName = (teamId?: string) => {
    if (!teamId) return "Sem equipa";
    const team = teams.find(t => t.id === teamId);
    return team ? team.name : "Equipa desconhecida";
  };
  // Função para obter o texto do papel do usuário
  const getRoleText = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "Administrador";
      case UserRole.TEAM_LEADER:
        return "Líder de Equipa";
      case UserRole.TEAM_MEMBER:
        return "Membro de Equipa";
      default:
        return "Sem papel";
    }
  };

  // Função para obter a variante do badge do papel
  const getRoleBadgeVariant = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "destructive";
      case UserRole.TEAM_LEADER:
        return "default";
      case UserRole.TEAM_MEMBER:
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <>
      <div className="relative flex flex-col gap-4">
        {/* Barra de ações em bulk - Responsiva */}
        {selectedUsers.length > 0 && (
          <div className="space-y-4 py-2">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-accent/50 border border-border rounded-lg">
              {/* Informação de seleção */}
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center size-8 rounded-full bg-primary/10">
                  <IconUsers className="size-4 text-primary" />
                </div>
                <div className="min-w-0">
                  <p className="text-sm font-medium text-foreground">
                    {selectedUsers.length} utilizador{selectedUsers.length > 1 ? 'es' : ''} selecionado{selectedUsers.length > 1 ? 's' : ''}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {getSelectedUserNames().join(", ")}
                    {selectedUsers.length > 3 && ` e mais ${selectedUsers.length - 3}`}
                  </p>
                </div>
              </div>

              {/* Ações - Layout responsivo */}
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                {/* Mobile: Menu de ações condensado */}
                {isMobile ? (
                  <div className="flex gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="flex-1 min-h-[44px]">
                          <IconMoreHorizontal className="size-4 mr-2" />
                          Ações
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-56">
                        {/* Alterar Papel */}
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.ADMIN)}
                        >
                          <IconUserShield className="mr-2 size-4" />
                          Definir como Administrador
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.TEAM_LEADER)}
                        >
                          <IconUserShield className="mr-2 size-4" />
                          Definir como Líder de Equipa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.TEAM_MEMBER)}
                        >
                          <IconUserShield className="mr-2 size-4" />
                          Definir como Membro de Equipa
                        </DropdownMenuItem>

                        {teams.length > 0 && (
                          <>
                            <DropdownMenuSeparator />
                            {teams.map(team => (
                              <DropdownMenuItem
                                key={team.id}
                                onClick={() => onBulkTeamAssign?.(selectedUsers, team.id)}
                              >
                                <IconUserPlus className="mr-2 size-4" />
                                Atribuir a {team.name}
                              </DropdownMenuItem>
                            ))}
                            <DropdownMenuItem
                              onClick={() => onBulkTeamAssign?.(selectedUsers, "")}
                            >
                              <IconUserMinus className="mr-2 size-4" />
                              Remover da Equipa
                            </DropdownMenuItem>
                          </>
                        )}

                        {getSelectedEmails().length > 0 && onBulkPasswordReset && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => onBulkPasswordReset(getSelectedEmails())}
                            >
                              <IconMail className="mr-2 size-4" />
                              Reenviar Passwords
                            </DropdownMenuItem>
                          </>
                        )}

                        {onBulkDelete && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={handleBulkDeleteConfirm}
                              className="text-destructive focus:text-destructive"
                            >
                              <IconTrash className="mr-2 size-4" />
                              Eliminar Utilizadores
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button variant="ghost" size="sm" onClick={clearSelection} className="min-h-[44px]">
                      <IconX className="size-4" />
                      <span className="sr-only">Cancelar seleção</span>
                    </Button>
                  </div>
                ) : (
                  /* Desktop: Botões individuais */
                  <>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <IconUserShield className="size-4 mr-2" />
                          Alterar Papel
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.ADMIN)}
                        >
                          Definir como Administrador
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.TEAM_LEADER)}
                        >
                          Definir como Líder de Equipa
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleBulkRoleChangeConfirm(UserRole.TEAM_MEMBER)}
                        >
                          Definir como Membro de Equipa
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    {teams.length > 0 && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <IconUserPlus className="size-4 mr-2" />
                            Atribuir Equipa
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {teams.map(team => (
                            <DropdownMenuItem
                              key={team.id}
                              onClick={() => onBulkTeamAssign?.(selectedUsers, team.id)}
                            >
                              {team.name}
                            </DropdownMenuItem>
                          ))}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onBulkTeamAssign?.(selectedUsers, "")}
                          >
                            Remover da Equipa
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}

                    {getSelectedEmails().length > 0 && onBulkPasswordReset && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onBulkPasswordReset(getSelectedEmails())}
                      >
                        <IconMail className="size-4 mr-2" />
                        Reenviar Passwords
                      </Button>
                    )}

                    {onBulkDelete && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleBulkDeleteConfirm}
                      >
                        <IconTrash className="size-4 mr-2" />
                        Eliminar
                      </Button>
                    )}

                    <Button variant="ghost" size="sm" onClick={clearSelection}>
                      <IconX className="size-4 mr-2" />
                      Cancelar
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

      <div className="overflow-hidden rounded-lg border">
        <Table className="w-full">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Selecionar todos os utilizadores"
                />
              </TableHead>
              <TableHead className="w-[22%]">Utilizador</TableHead>
              <TableHead className="w-[12%]">Matrícula</TableHead>
              <TableHead className="w-[16%]">Categoria</TableHead>
              <TableHead className="w-[13%]">Papel</TableHead>
              <TableHead className="w-[18%]">Equipa</TableHead>
              <TableHead className="w-[9%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center text-muted-foreground">
                  Nenhum utilizador encontrado.
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} className="hover:bg-muted/50">
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                      aria-label={`Selecionar ${user.name}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="size-8">
                        <AvatarFallback>
                          {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">{user.name}</div>
                        {user.email && (
                          <div className="text-sm text-muted-foreground truncate">{user.email}</div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.registrationNumber || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.category || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role) as any}>
                      {getRoleText(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">
                      {user.teamId ? (
                        <Badge variant="outline" className="font-normal">
                          {getTeamName(user.teamId)}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">Sem equipa</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => onEditProfile(user)}>
                          <IconUserEdit className="mr-2 size-4" />
                          Editar Perfil
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditRole(user)}>
                          <IconUserShield className="mr-2 size-4" />
                          Editar Papel
                        </DropdownMenuItem>
                        {onSendPasswordReset && user.email && (
                          <DropdownMenuItem
                            onClick={() => onSendPasswordReset(user.email || "")}
                          >
                            <IconMail className="mr-2 size-4" />
                            Reenviar Email
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem
                            onClick={() => onDelete(user)}
                            className="text-destructive focus:text-destructive"
                          >
                            <IconTrash className="mr-2 size-4" />
                            Excluir Utilizador
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>

    {/* Diálogo de Confirmação para Eliminação em Bulk */}
    <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Confirmar Eliminação em Bulk</DialogTitle>
          <DialogDescription>
            Tem certeza que deseja eliminar {selectedUsers.length} utilizador{selectedUsers.length > 1 ? 'es' : ''}?
          </DialogDescription>
        </DialogHeader>

        <Alert variant="destructive">
          <IconAlertTriangle className="size-4" />
          <AlertDescription>
            Esta ação é irreversível. Os utilizadores selecionados serão permanentemente eliminados do sistema.
          </AlertDescription>
        </Alert>

        <div className="space-y-2">
          <p className="text-sm font-medium">Utilizadores a eliminar:</p>
          <div className="max-h-32 overflow-y-auto space-y-1 p-2 bg-muted/50 rounded-md">
            {users
              .filter(user => selectedUsers.includes(user.id))
              .map(user => (
                <div key={user.id} className="flex items-center gap-2 text-sm">
                  <Avatar className="size-6">
                    <AvatarFallback className="text-xs">
                      {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="truncate">{user.name}</span>
                  {user.email && (
                    <span className="text-muted-foreground text-xs">({user.email})</span>
                  )}
                </div>
              ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(false)}>
            Cancelar
          </Button>
          <Button variant="destructive" onClick={confirmBulkDelete}>
            <IconTrash className="size-4 mr-2" />
            Eliminar {selectedUsers.length} Utilizador{selectedUsers.length > 1 ? 'es' : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Diálogo de Confirmação para Alteração de Papel em Bulk */}
    <Dialog open={isRoleChangeConfirmOpen} onOpenChange={setIsRoleChangeConfirmOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Confirmar Alteração de Papel</DialogTitle>
          <DialogDescription>
            Tem certeza que deseja alterar o papel de {selectedUsers.length} utilizador{selectedUsers.length > 1 ? 'es' : ''} para{" "}
            <strong>{pendingRoleChange && getRoleDisplayText(pendingRoleChange.role)}</strong>?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-2">
          <p className="text-sm font-medium">Utilizadores afetados:</p>
          <div className="max-h-32 overflow-y-auto space-y-1 p-2 bg-muted/50 rounded-md">
            {users
              .filter(user => selectedUsers.includes(user.id))
              .map(user => (
                <div key={user.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <Avatar className="size-6">
                      <AvatarFallback className="text-xs">
                        {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="truncate">{user.name}</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <Badge variant={getRoleBadgeVariant(user.role) as any} className="text-xs">
                      {getRoleText(user.role)}
                    </Badge>
                    <span className="text-muted-foreground">→</span>
                    <Badge variant={getRoleBadgeVariant(pendingRoleChange?.role) as any} className="text-xs">
                      {pendingRoleChange && getRoleDisplayText(pendingRoleChange.role)}
                    </Badge>
                  </div>
                </div>
              ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsRoleChangeConfirmOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={confirmBulkRoleChange}>
            <IconCheck className="size-4 mr-2" />
            Confirmar Alteração
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </>
  );
}
