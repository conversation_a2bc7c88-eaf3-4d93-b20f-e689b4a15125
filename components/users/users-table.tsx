"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import {
  IconDotsVertical,
  IconEdit,
  IconUserShield,
  IconUserEdit,
  IconTrash,
  IconMail,
  IconUsers,
  IconUserPlus,
  IconUserMinus,
  IconCheck,
  IconX
} from "@tabler/icons-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { UserRole } from "@/types/team";

interface User {
  id: string;
  name: string;
  email?: string;
  registrationNumber?: string;
  category?: string;
  role?: UserRole;
  teamId?: string;
  teamName?: string;
}

interface UsersTableProps {
  users: User[];
  onEditRole: (user: User) => void;
  onEditProfile: (user: User) => void;
  onDelete?: (user: User) => void;
  onSendPasswordReset?: (email: string) => void;
  teams?: { id: string; name: string }[];
  // Bulk actions
  onBulkDelete?: (userIds: string[]) => void;
  onBulkRoleChange?: (userIds: string[], role: UserRole, teamId?: string) => void;
  onBulkTeamAssign?: (userIds: string[], teamId: string) => void;
  onBulkPasswordReset?: (emails: string[]) => void;
}

export function UsersTable({
  users,
  onEditRole,
  onEditProfile,
  onDelete,
  onSendPasswordReset,
  teams = [],
  onBulkDelete,
  onBulkRoleChange,
  onBulkTeamAssign,
  onBulkPasswordReset
}: UsersTableProps) {
  // Estado para seleção múltipla
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);

  // Funções de manipulação de seleção
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
      setIsAllSelected(true);
    } else {
      setSelectedUsers([]);
      setIsAllSelected(false);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      const newSelected = [...selectedUsers, userId];
      setSelectedUsers(newSelected);
      setIsAllSelected(newSelected.length === users.length);
    } else {
      const newSelected = selectedUsers.filter(id => id !== userId);
      setSelectedUsers(newSelected);
      setIsAllSelected(false);
    }
  };

  const clearSelection = () => {
    setSelectedUsers([]);
    setIsAllSelected(false);
  };

  // Obter emails dos utilizadores selecionados
  const getSelectedEmails = () => {
    return users
      .filter(user => selectedUsers.includes(user.id) && user.email)
      .map(user => user.email!)
      .filter(Boolean);
  };

  // Função para obter o nome da equipe pelo ID
  const getTeamName = (teamId?: string) => {
    if (!teamId) return "Sem equipa";
    const team = teams.find(t => t.id === teamId);
    return team ? team.name : "Equipa desconhecida";
  };
  // Função para obter o texto do papel do usuário
  const getRoleText = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "Administrador";
      case UserRole.TEAM_LEADER:
        return "Líder de Equipa";
      case UserRole.TEAM_MEMBER:
        return "Membro de Equipa";
      default:
        return "Sem papel";
    }
  };

  // Função para obter a variante do badge do papel
  const getRoleBadgeVariant = (role?: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "destructive";
      case UserRole.TEAM_LEADER:
        return "default";
      case UserRole.TEAM_MEMBER:
        return "secondary";
      default:
        return "outline";
    }
  };

  return (
    <div className="relative flex flex-col gap-4">
      {/* Barra de ações em bulk */}
      {selectedUsers.length > 0 && (
        <div className="flex items-center justify-between p-3 bg-primary/10 border border-primary/20 rounded-lg">
          <div className="flex items-center gap-2">
            <IconUsers className="size-4" />
            <span className="text-sm font-medium">
              {selectedUsers.length} utilizador{selectedUsers.length > 1 ? 'es' : ''} selecionado{selectedUsers.length > 1 ? 's' : ''}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {/* Ações em bulk */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <IconUserShield className="size-4 mr-2" />
                  Alterar Papel
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => onBulkRoleChange?.(selectedUsers, UserRole.ADMIN)}
                >
                  Definir como Administrador
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onBulkRoleChange?.(selectedUsers, UserRole.TEAM_LEADER)}
                >
                  Definir como Líder de Equipa
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onBulkRoleChange?.(selectedUsers, UserRole.TEAM_MEMBER)}
                >
                  Definir como Membro de Equipa
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {teams.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <IconUserPlus className="size-4 mr-2" />
                    Atribuir Equipa
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {teams.map(team => (
                    <DropdownMenuItem
                      key={team.id}
                      onClick={() => onBulkTeamAssign?.(selectedUsers, team.id)}
                    >
                      {team.name}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => onBulkTeamAssign?.(selectedUsers, "")}
                  >
                    Remover da Equipa
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {getSelectedEmails().length > 0 && onBulkPasswordReset && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkPasswordReset(getSelectedEmails())}
              >
                <IconMail className="size-4 mr-2" />
                Reenviar Passwords
              </Button>
            )}

            {onBulkDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onBulkDelete(selectedUsers)}
              >
                <IconTrash className="size-4 mr-2" />
                Eliminar
              </Button>
            )}

            <Button variant="ghost" size="sm" onClick={clearSelection}>
              <IconX className="size-4 mr-2" />
              Cancelar
            </Button>
          </div>
        </div>
      )}

      <div className="overflow-hidden rounded-lg border">
        <Table className="w-full">
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Selecionar todos os utilizadores"
                />
              </TableHead>
              <TableHead className="w-[22%]">Utilizador</TableHead>
              <TableHead className="w-[12%]">Matrícula</TableHead>
              <TableHead className="w-[16%]">Categoria</TableHead>
              <TableHead className="w-[13%]">Papel</TableHead>
              <TableHead className="w-[18%]">Equipa</TableHead>
              <TableHead className="w-[9%] text-right"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="**:data-[slot=table-cell]:first:w-8">
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center text-muted-foreground">
                  Nenhum utilizador encontrado.
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} className="hover:bg-muted/50">
                  <TableCell>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                      aria-label={`Selecionar ${user.name}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="size-8">
                        <AvatarFallback>
                          {user.name.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">{user.name}</div>
                        {user.email && (
                          <div className="text-sm text-muted-foreground truncate">{user.email}</div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.registrationNumber || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">{user.category || "N/A"}</div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role) as any}>
                      {getRoleText(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="truncate">
                      {user.teamId ? (
                        <Badge variant="outline" className="font-normal">
                          {getTeamName(user.teamId)}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">Sem equipa</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="data-[state=open]:bg-muted text-muted-foreground size-8 ml-auto"
                          size="icon"
                        >
                          <IconDotsVertical className="size-4" />
                          <span className="sr-only">Abrir menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => onEditProfile(user)}>
                          <IconUserEdit className="mr-2 size-4" />
                          Editar Perfil
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditRole(user)}>
                          <IconUserShield className="mr-2 size-4" />
                          Editar Papel
                        </DropdownMenuItem>
                        {onSendPasswordReset && user.email && (
                          <DropdownMenuItem
                            onClick={() => onSendPasswordReset(user.email || "")}
                          >
                            <IconMail className="mr-2 size-4" />
                            Reenviar Email
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem
                            onClick={() => onDelete(user)}
                            className="text-destructive focus:text-destructive"
                          >
                            <IconTrash className="mr-2 size-4" />
                            Excluir Utilizador
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
