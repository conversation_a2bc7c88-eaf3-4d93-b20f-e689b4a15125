"use client";

import { useState, useRef, useEffect } from "react";
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from "@/lib/toast-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { IconUpload, IconFile, IconX, IconPlus, IconTrash } from "@tabler/icons-react";
import { storage, auth } from "@/lib/firebase";
import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { useAuth } from "@/contexts/auth-context";

interface DocumentItem {
  id: string;
  file: File;
  title: string;
  uploading: boolean;
  progress: number;
  uploaded: boolean;
  url?: string;
}

interface MultipleFileUploadProps {
  onUploadComplete: (filesData: {
    name: string;
    title: string;
    url: string;
    type: string;
    size: number;
    uploadedAt: Date;
    uploadedBy: string;
    teamId?: string;
    groupId: string;
    groupTitle: string;
  }[]) => void;
}

export function MultipleFileUpload({ onUploadComplete }: MultipleFileUploadProps) {
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [groupTitle, setGroupTitle] = useState("");
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const progressToastRef = useRef<string | number | null>(null);
  const { user, userTeamId } = useAuth();

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
        progressToastRef.current = null;
      }
    };
  }, []);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const addDocument = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      const newDocuments = newFiles.map(file => ({
        id: generateId(),
        file,
        title: file.name.replace(/\.[^/.]+$/, ""), // Remove extension for default title
        uploading: false,
        progress: 0,
        uploaded: false,
      }));
      
      setDocuments(prev => [...prev, ...newDocuments]);
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const removeDocument = (id: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== id));
  };

  const updateDocumentTitle = (id: string, title: string) => {
    setDocuments(prev => 
      prev.map(doc => 
        doc.id === id ? { ...doc, title } : doc
      )
    );
  };

  const getFileIcon = (file: File) => {
    const fileType = file.type;
    if (fileType.includes("pdf")) {
      return <IconFile className="text-red-500" />;
    } else if (fileType.includes("spreadsheet") || fileType.includes("excel") || file.name.endsWith(".xlsx") || file.name.endsWith(".xls")) {
      return <IconFile className="text-green-500" />;
    } else if (fileType.includes("word") || file.name.endsWith(".docx") || file.name.endsWith(".doc")) {
      return <IconFile className="text-blue-500" />;
    } else {
      return <IconFile className="text-gray-500" />;
    }
  };

  const uploadDocument = async (document: DocumentItem, sharedGroupId: string): Promise<{
    name: string;
    title: string;
    url: string;
    type: string;
    size: number;
    uploadedAt: Date;
    uploadedBy: string;
    teamId?: string;
    groupId: string;
    groupTitle: string;
  }> => {
    return new Promise((resolve, reject) => {
      const fileExtension = document.file.name.split('.').pop();
      const fileName = `${Date.now()}_${document.title.replace(/\s+/g, '_')}.${fileExtension}`;
      const filePath = `formularios/${fileName}`;
      const storageRef = ref(storage, filePath);
      const uploadTask = uploadBytesResumable(storageRef, document.file);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          const progress = Math.round(
            (snapshot.bytesTransferred / snapshot.totalBytes) * 100
          );

          setDocuments(prev =>
            prev.map(doc =>
              doc.id === document.id ? { ...doc, progress } : doc
            )
          );
        },
        (error) => {
          console.error("Erro no upload:", error);
          reject(error);
        },
        async () => {
          try {
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

            const fileData = {
              name: document.file.name,
              title: document.title,
              url: downloadURL,
              type: document.file.type,
              size: document.file.size,
              uploadedAt: new Date(),
              uploadedBy: user!.uid,
              groupId: sharedGroupId,
              groupTitle: groupTitle,
              ...(userTeamId && { teamId: userTeamId }),
            };

            setDocuments(prev =>
              prev.map(doc =>
                doc.id === document.id ? { ...doc, uploaded: true, url: downloadURL } : doc
              )
            );

            resolve(fileData);
          } catch (error) {
            reject(error);
          }
        }
      );
    });
  };

  const handleUploadAll = async () => {
    if (documents.length === 0) {
      showErrorToast("Por favor, adicione pelo menos um documento");
      return;
    }

    if (!groupTitle.trim()) {
      showErrorToast("Por favor, insira um título para o grupo de documentos");
      return;
    }

    if (!user) {
      showErrorToast("É necessário estar autenticado para carregar ficheiros");
      return;
    }

    // Check if all documents have titles
    const documentsWithoutTitle = documents.filter(doc => !doc.title.trim());
    if (documentsWithoutTitle.length > 0) {
      showErrorToast("Por favor, insira títulos para todos os documentos");
      return;
    }

    setUploading(true);
    
    // Mark all documents as uploading
    setDocuments(prev => 
      prev.map(doc => ({ ...doc, uploading: true }))
    );

    const groupId = generateId();
    progressToastRef.current = showLoadingToast("A carregar documentos...");

    try {
      const uploadPromises = documents.map(async (document) => {
        return await uploadDocument(document, groupId);
      });

      const uploadedFiles = await Promise.all(uploadPromises);

      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
        progressToastRef.current = null;
      }

      onUploadComplete(uploadedFiles);
      showSuccessToast(`${uploadedFiles.length} documento(s) carregado(s) com sucesso`);

      // Reset form
      setDocuments([]);
      setGroupTitle("");
      setUploading(false);
    } catch (error) {
      console.error("Erro ao carregar documentos:", error);
      
      if (progressToastRef.current) {
        dismissToast(progressToastRef.current);
        progressToastRef.current = null;
      }
      
      showErrorToast("Erro ao carregar documentos");
      setUploading(false);
      
      // Reset uploading state
      setDocuments(prev => 
        prev.map(doc => ({ ...doc, uploading: false, progress: 0 }))
      );
    }
  };

  return (
    <div className="space-y-4 py-2">
      {/* Group Title */}
      <div className="space-y-2">
        <Label htmlFor="groupTitle">Título do Grupo de Documentos</Label>
        <Input
          id="groupTitle"
          placeholder="Insira um título para este grupo de documentos"
          value={groupTitle}
          onChange={(e) => setGroupTitle(e.target.value)}
          disabled={uploading}
        />
      </div>

      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        disabled={uploading}
        className="hidden"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
        multiple
      />

      {/* Documents List */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label>Documentos ({documents.length})</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addDocument}
            disabled={uploading}
          >
            <IconPlus className="size-4 mr-2" />
            Adicionar Documento
          </Button>
        </div>

        {documents.length === 0 ? (
          <div
            className="w-full h-24 flex flex-col items-center justify-center border border-dashed rounded-md cursor-pointer hover:bg-accent/50 transition-colors"
            onClick={addDocument}
          >
            <IconUpload className="size-6 mb-2 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Clique para adicionar documentos
            </span>
          </div>
        ) : (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {documents.map((document) => (
              <Card key={document.id} className="p-3">
                <CardContent className="p-0">
                  <div className="flex items-center gap-3">
                    {getFileIcon(document.file)}
                    <div className="flex-1 space-y-2">
                      <Input
                        placeholder="Título do documento"
                        value={document.title}
                        onChange={(e) => updateDocumentTitle(document.id, e.target.value)}
                        disabled={uploading}
                        className="text-sm"
                      />
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{document.file.name}</span>
                        <span>•</span>
                        <span>{(document.file.size / 1024 / 1024).toFixed(2)} MB</span>
                        {document.uploading && (
                          <>
                            <span>•</span>
                            <span>{document.progress}%</span>
                          </>
                        )}
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeDocument(document.id)}
                      disabled={uploading}
                    >
                      <IconTrash className="size-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <p className="text-xs text-muted-foreground">
        Formatos suportados: PDF, Word, Excel, PowerPoint, TXT
      </p>

      {/* Upload Button */}
      <div className="flex justify-end gap-2 mt-6">
        <Button
          onClick={handleUploadAll}
          disabled={uploading || documents.length === 0 || !groupTitle.trim()}
        >
          {uploading ? "A carregar..." : `Carregar ${documents.length} Documento(s)`}
        </Button>
      </div>
    </div>
  );
}
